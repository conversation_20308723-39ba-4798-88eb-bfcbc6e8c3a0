#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机数测试工具
用于分析随机种子的质量和安全性
"""

import sys
import math
import numpy as np
from collections import Counter, defaultdict
from scipy import stats
import argparse
import os


class Colors:
    """颜色定义"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # 结束颜色

    @staticmethod
    def red(text):
        return f"{Colors.RED}{text}{Colors.END}"

    @staticmethod
    def green(text):
        return f"{Colors.GREEN}{text}{Colors.END}"

    @staticmethod
    def yellow(text):
        return f"{Colors.YELLOW}{text}{Colors.END}"

    @staticmethod
    def bold(text):
        return f"{Colors.BOLD}{text}{Colors.END}"


def format_result(passed, test_name=""):
    """格式化测试结果"""
    if passed:
        return Colors.green("✓ PASS")
    else:
        return Colors.red("✗ FAIL")


class RandomSeedAnalyzer:
    """随机种子分析器"""

    def __init__(self):
        self.seeds = []
        self.seed_bytes = []
        self.seed_length = 0

    def load_seeds_from_file(self, filename):
        """从文件加载种子值"""
        print(f"[+] 分析 {filename} 中的随机种子值...")

        if not os.path.exists(filename):
            print(f"[-] 错误: 文件 {filename} 不存在")
            return False

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 过滤空行和注释
            valid_lines = [line.strip() for line in lines if line.strip() and not line.strip().startswith('#')]

            for line in valid_lines:
                # 移除可能的前缀和后缀
                seed = line.strip().replace(' ', '').replace('\t', '')

                # 验证是否为有效的十六进制字符串
                try:
                    int(seed, 16)
                    self.seeds.append(seed.upper())
                except ValueError:
                    print(f"[!] 警告: 跳过无效的种子值: {seed}")
                    continue

            if not self.seeds:
                print("[-] 错误: 未找到有效的种子值")
                return False

            print(f"[+] 已加载 {len(self.seeds)} 个种子值")

            # 检查种子长度一致性
            lengths = [len(seed) for seed in self.seeds]
            if len(set(lengths)) > 1:
                print(f"[!] 警告: 种子长度不一致，范围: {min(lengths)}-{max(lengths)} 字符")

            self.seed_length = max(lengths) // 2  # 字节长度
            print(f"种子数量: {len(self.seeds)}")
            print(f"[+] 种子长度: {self.seed_length} 字节 ({self.seed_length * 8} 位)")

            # 根据长度给出评估
            self._evaluate_seed_length()

            # 转换为字节数组用于分析
            self._convert_to_bytes()

            return True

        except Exception as e:
            print(f"[-] 读取文件时发生错误: {e}")
            return False

    def load_seeds_from_list(self, seed_list):
        """从列表加载种子值"""
        print(f"[+] 分析提供的 {len(seed_list)} 个随机种子值...")

        for seed in seed_list:
            # 移除可能的前缀和后缀
            clean_seed = seed.strip().replace(' ', '').replace('\t', '')

            # 验证是否为有效的十六进制字符串
            try:
                int(clean_seed, 16)
                self.seeds.append(clean_seed.upper())
            except ValueError:
                print(f"[!] 警告: 跳过无效的种子值: {seed}")
                continue

        if not self.seeds:
            print("[-] 错误: 未找到有效的种子值")
            return False

        print(f"[+] 已加载 {len(self.seeds)} 个种子值")

        # 检查种子长度一致性
        lengths = [len(seed) for seed in self.seeds]
        if len(set(lengths)) > 1:
            print(f"[!] 警告: 种子长度不一致，范围: {min(lengths)}-{max(lengths)} 字符")

        self.seed_length = max(lengths) // 2  # 字节长度
        print(f"种子数量: {len(self.seeds)}")
        print(f"[+] 种子长度: {self.seed_length} 字节 ({self.seed_length * 8} 位)")

        # 根据长度给出评估
        self._evaluate_seed_length()

        # 转换为字节数组用于分析
        self._convert_to_bytes()

        return True

    def _evaluate_seed_length(self):
        """评估种子长度"""
        if self.seed_length == 16:
            print("【16字节(128位)种子 - 标准长度】")
        elif self.seed_length == 32:
            print("【32字节(256位)种子 - 高强度长度】")
        elif self.seed_length == 8:
            print("【8字节(64位)种子 - 较短长度】")
        elif self.seed_length < 8:
            print("【警告: 种子长度过短，可能存在安全风险】")
        else:
            print(f"【{self.seed_length}字节({self.seed_length * 8}位)种子 - 自定义长度】")

    def _convert_to_bytes(self):
        """将十六进制种子转换为字节数组"""
        self.seed_bytes = []
        for seed in self.seeds:
            # 确保种子长度为偶数
            if len(seed) % 2 != 0:
                seed = '0' + seed

            byte_array = []
            for i in range(0, len(seed), 2):
                byte_array.append(int(seed[i:i+2], 16))
            self.seed_bytes.append(byte_array)

    def _evaluate_sample_size(self):
        """评估样本量"""
        sample_count = len(self.seeds)

        # 基于种子长度确定推荐样本量
        if self.seed_length <= 8:
            recommended = 10000
        elif self.seed_length <= 16:
            recommended = 20000
        else:
            recommended = 50000

        ratio = sample_count / recommended

        print("\n【当前样本评估】:")
        print(f"  • 当前样本量: {sample_count:,} 个种子")

        if ratio >= 5.0:
            status = "优秀 (超过推荐量{:.1f}倍)".format(ratio)
            advice = "样本量充足，可进行高精度安全评估。"
        elif ratio >= 2.0:
            status = "良好 (超过推荐量{:.1f}倍)".format(ratio)
            advice = "样本量充足，分析结果可靠。"
        elif ratio >= 1.0:
            status = "合格 (达到推荐量{:.1f}倍)".format(ratio)
            advice = "样本量基本满足分析需求。"
        elif ratio >= 0.5:
            status = "偏少 (推荐量的{:.1f}倍)".format(ratio)
            advice = "建议增加样本量以提高分析准确性。"
        else:
            status = "不足 (推荐量的{:.1f}倍)".format(ratio)
            advice = "样本量严重不足，分析结果可能不准确。"

        print(f"  • 状态: {status}")
        print(f"  • 建议: {advice}")

    def basic_statistics(self):
        """基本统计信息"""
        print("\n=== 基本统计信息 ===")
        print(f"有效种子值数量: {len(self.seeds)}")

        if self.seeds:
            # 转换为整数进行比较
            int_seeds = [int(seed, 16) for seed in self.seeds]
            min_seed = min(int_seeds)
            max_seed = max(int_seeds)

            print(f"最小值: {min_seed:X}")
            print(f"最大值: {max_seed:X}")

        # 评估样本量
        self._evaluate_sample_size()











    def run_full_analysis(self):
        """运行完整分析"""
        print("=" * 80)
        print(Colors.bold("随机种子安全性分析报告"))
        print("=" * 80)

        # 存储各项测试结果和详细数据
        test_results = {}
        test_details = {}

        # 基本统计
        self.basic_statistics()

        # 执行各项测试并收集详细数据
        print("\n正在执行安全性测试...")

        # 熵分析
        result, details = self._entropy_test_with_details()
        test_results['熵分析'] = result
        test_details['熵分析'] = details

        # 序列模式分析
        result, details = self._sequence_pattern_test_with_details()
        test_results['序列模式'] = result
        test_details['序列模式'] = details

        # 线性相关性分析
        result, details = self._linear_correlation_test_with_details()
        test_results['线性相关性'] = result
        test_details['线性相关性'] = details

        # 字节分布分析
        result, details = self._byte_distribution_test_with_details()
        test_results['字节分布'] = result
        test_details['字节分布'] = details

        # 基于时间的模式分析
        result, details = self._time_based_pattern_test_with_details()
        test_results['时间模式'] = result
        test_details['时间模式'] = details

        # 重复模式分析
        result, details = self._repetition_test_with_details()
        test_results['重复模式'] = result
        test_details['重复模式'] = details

        # 显示详细结果表格
        self._display_detailed_table(test_results, test_details)

    def _display_summary(self, test_results):
        """显示测试总结 - 表格形式"""
        print("\n" + "=" * 80)
        print(Colors.bold("随机种子安全性测试结果"))
        print("=" * 80)

        # 表格头部
        print(f"{'测试项目':<20} {'状态':<15} {'描述':<35}")
        print("-" * 80)

        # 测试项目描述
        test_descriptions = {
            '熵分析': '检查随机性质量',
            '序列模式': '检测连续值模式',
            '线性相关性': '检测线性趋势',
            '字节分布': '检查字节均匀性',
            '时间模式': '检测时间戳模式',
            '重复模式': '检查重复值'
        }

        passed_count = 0
        total_count = len(test_results)

        for test_name, passed in test_results.items():
            if passed:
                status = Colors.green("✓ PASS")
                passed_count += 1
            else:
                status = Colors.red("✗ FAIL")

            description = test_descriptions.get(test_name, "未知测试")
            print(f"{test_name:<20} {status:<25} {description:<35}")

        print("-" * 80)

        # 整体评估
        pass_rate = (passed_count / total_count) * 100
        print(f"{'整体评估':<20} ", end="")

        if passed_count == total_count:
            overall_status = Colors.green("✓ PASS")
            overall_desc = Colors.green("所有测试通过，随机种子质量优秀")
        elif pass_rate >= 80:
            overall_status = Colors.yellow("⚠ WARN")
            overall_desc = Colors.yellow(f"通过率 {pass_rate:.0f}%，存在轻微问题")
        else:
            overall_status = Colors.red("✗ FAIL")
            overall_desc = Colors.red(f"通过率 {pass_rate:.0f}%，存在严重问题")

        print(f"{overall_status:<25} {overall_desc}")

        print("=" * 80)

        # 统计信息
        print(f"\n统计信息:")
        print(f"  • 总测试项目: {total_count}")
        print(f"  • 通过项目: {Colors.green(str(passed_count))}")
        print(f"  • 失败项目: {Colors.red(str(total_count - passed_count))}")
        print(f"  • 通过率: {Colors.green(f'{pass_rate:.1f}%') if pass_rate == 100 else Colors.yellow(f'{pass_rate:.1f}%')}")

        if passed_count < total_count:
            print(f"\n{Colors.yellow('建议:')}")
            print("  • 检查随机数生成器的实现")
            print("  • 验证种子来源的随机性")
            print("  • 考虑使用更强的随机数生成算法")

    def _entropy_test(self):
        """熵测试，返回Pass/Fail"""
        if not self.seed_bytes:
            print("❌ 错误: 没有可分析的种子数据")
            return False

        # 将所有种子的字节合并
        all_bytes = []
        for seed_byte_array in self.seed_bytes:
            all_bytes.extend(seed_byte_array)

        # 计算字节频率
        byte_counts = Counter(all_bytes)
        total_bytes = len(all_bytes)

        # 计算香农熵
        shannon_entropy = 0
        for count in byte_counts.values():
            probability = count / total_bytes
            if probability > 0:
                shannon_entropy -= probability * math.log2(probability)

        # 最大可能熵（8位字节）
        max_entropy = 8.0
        entropy_ratio = shannon_entropy / max_entropy

        # Pass/Fail 判断
        passed = entropy_ratio >= 0.9
        result = format_result(passed)

        print(f"  香农熵: {shannon_entropy:.4f} bits (最大: {max_entropy:.4f})")
        print(f"  熵比率: {entropy_ratio:.4f} (阈值: ≥0.9)")
        print(f"  测试结果: {result}")

        if not passed:
            print(f"  {Colors.yellow('⚠ 警告: 熵比率过低，可能存在随机性问题')}")

        return passed

    def _sequence_pattern_test(self):
        """序列模式测试，返回Pass/Fail"""
        if len(self.seeds) < 2:
            print("❌ 错误: 需要至少2个种子值进行序列分析")
            return False

        # 转换为整数并计算连续差值
        int_seeds = [int(seed, 16) for seed in self.seeds]
        differences = []

        for i in range(1, len(int_seeds)):
            diff = int_seeds[i] - int_seeds[i-1]
            differences.append(diff)

        # 统计差值频率
        diff_counts = Counter(differences)
        most_common_diff, most_common_count = diff_counts.most_common(1)[0]
        percentage = (most_common_count / len(differences)) * 100

        # Pass/Fail 判断
        passed = percentage < 20
        result = format_result(passed)

        print(f"  最常见差值占比: {percentage:.2f}% (阈值: <20%)")
        print(f"  差值: {most_common_diff}")
        print(f"  测试结果: {result}")

        if not passed:
            print(f"  {Colors.yellow('⚠ 警告: 最常见差值占比过高，可能存在模式')}")

        return passed

    def _linear_correlation_test(self):
        """线性相关性测试，返回Pass/Fail"""
        if len(self.seeds) < 3:
            print("❌ 错误: 需要至少3个种子值进行相关性分析")
            return False

        # 转换为整数
        int_seeds = [int(seed, 16) for seed in self.seeds]
        indices = list(range(len(int_seeds)))

        # 计算皮尔逊相关系数
        try:
            correlation, _ = stats.pearsonr(indices, int_seeds)

            # Pass/Fail 判断
            passed = abs(correlation) < 0.3
            result = format_result(passed)

            print(f"  线性相关系数: {correlation:.4f} (阈值: |r| < 0.3)")
            print(f"  测试结果: {result}")

            if not passed:
                print(f"  {Colors.yellow('⚠ 警告: 相关系数过高，可能存在线性趋势')}")

            return passed

        except Exception as e:
            print(f"❌ 计算相关性时发生错误: {e}")
            return False

    def _byte_distribution_test(self):
        """字节分布测试，返回Pass/Fail"""
        if not self.seed_bytes:
            print("❌ 错误: 没有可分析的种子数据")
            return False

        # 将所有种子的字节合并
        all_bytes = []
        for seed_byte_array in self.seed_bytes:
            all_bytes.extend(seed_byte_array)

        # 基本统计
        mean_value = np.mean(all_bytes)
        std_value = np.std(all_bytes)

        # 计算字节熵
        byte_counts = Counter(all_bytes)
        total_bytes = len(all_bytes)
        byte_entropy = 0

        for count in byte_counts.values():
            probability = count / total_bytes
            if probability > 0:
                byte_entropy -= probability * math.log2(probability)

        # 卡方检验 - 适合离散数据的均匀性检验
        try:
            # 统计每个字节值(0-255)的出现次数
            byte_counts = Counter(all_bytes)

            # 创建完整的频率数组(0-255)
            observed_frequencies = []
            for i in range(256):
                observed_frequencies.append(byte_counts.get(i, 0))

            # 期望频率(均匀分布)
            expected_frequency = len(all_bytes) / 256
            expected_frequencies = [expected_frequency] * 256

            # 卡方检验
            _, p_value = stats.chisquare(observed_frequencies, expected_frequencies)

            # Pass/Fail 判断
            passed = p_value > 0.05 and byte_entropy > 7.5
            result = format_result(passed)

            print(f"  字节平均值: {mean_value:.2f} (期望: 127.5)")
            print(f"  字节熵: {byte_entropy:.4f} bits (阈值: >7.5)")
            print(f"  均匀性 p值: {p_value:.4f} (阈值: >0.05)")
            print(f"  测试结果: {result}")

            if not passed:
                if p_value <= 0.05:
                    print(f"  {Colors.yellow('⚠ 警告: p值过低，分布偏离均匀')}")
                if byte_entropy <= 7.5:
                    print(f"  {Colors.yellow('⚠ 警告: 字节熵过低，随机性不足')}")

            return passed

        except Exception as e:
            print(f"❌ 进行均匀性检验时发生错误: {e}")
            return False

    def _time_based_pattern_test(self):
        """时间模式测试，返回Pass/Fail"""
        if len(self.seeds) < 10:
            print("❌ 错误: 需要至少10个种子值进行时间模式分析")
            return False

        # 转换为整数并计算连续差值
        int_seeds = [int(seed, 16) for seed in self.seeds]
        differences = []

        for i in range(1, len(int_seeds)):
            diff = int_seeds[i] - int_seeds[i-1]
            differences.append(diff)

        # 检查是否存在固定增量模式
        diff_counts = Counter(differences)

        # 计算差值的标准差
        diff_std = np.std(differences)
        diff_mean = np.mean(differences)

        # 检查最常见差值的占比
        most_common_count = diff_counts.most_common(1)[0][1]
        common_ratio = most_common_count / len(differences)

        # 检查是否存在类似时间戳的模式
        is_time_based = False

        # 如果差值变化很小且有明显的主导差值，可能是基于时间的
        if diff_std < abs(diff_mean) * 0.1 and common_ratio > 0.3:
            is_time_based = True

        # 检查是否存在周期性模式
        if len(set(differences[:min(100, len(differences))])) < 5:
            is_time_based = True

        # Pass/Fail 判断
        passed = not is_time_based
        result = format_result(passed)

        print(f"  差值变异系数: {(diff_std/abs(diff_mean)):.4f} (阈值: >0.1)")
        print(f"  主导差值占比: {common_ratio:.4f} (阈值: <0.3)")
        print(f"  时间模式检测: {'是' if is_time_based else '否'}")
        print(f"  测试结果: {result}")

        if not passed:
            print(f"  {Colors.yellow('⚠ 警告: 检测到可能的时间戳模式')}")

        return passed

    def _repetition_test(self):
        """重复模式测试，返回Pass/Fail"""
        if not self.seeds:
            print("❌ 错误: 没有可分析的种子数据")
            return False

        # 检查重复值
        seed_counts = Counter(self.seeds)
        duplicates = {seed: count for seed, count in seed_counts.items() if count > 1}

        duplicate_count = sum(count - 1 for count in duplicates.values())
        duplicate_rate = (duplicate_count / len(self.seeds)) * 100

        # Pass/Fail 判断
        passed = duplicate_rate < 1.0
        result = format_result(passed)

        print(f"  重复值数量: {duplicate_count}")
        print(f"  重复率: {duplicate_rate:.2f}% (阈值: <1.0%)")
        print(f"  测试结果: {result}")

        if not passed:
            print(f"  {Colors.yellow('⚠ 警告: 重复率过高，存在安全风险')}")

            # 显示重复值详情
            if duplicates and len(duplicates) <= 3:
                print(f"  重复值详情:")
                for seed, count in duplicates.items():
                    print(f"    {seed}: 出现 {count} 次")
            elif duplicates:
                print(f"  发现 {len(duplicates)} 个不同的重复值")

        return passed

    def _entropy_test_with_details(self):
        """熵测试，返回Pass/Fail和详细数据"""
        if not self.seed_bytes:
            return False, {"error": "没有可分析的种子数据"}

        # 将所有种子的字节合并
        all_bytes = []
        for seed_byte_array in self.seed_bytes:
            all_bytes.extend(seed_byte_array)

        # 计算字节频率
        byte_counts = Counter(all_bytes)
        total_bytes = len(all_bytes)

        # 计算香农熵
        shannon_entropy = 0
        for count in byte_counts.values():
            probability = count / total_bytes
            if probability > 0:
                shannon_entropy -= probability * math.log2(probability)

        # 最大可能熵（8位字节）
        max_entropy = 8.0
        entropy_ratio = shannon_entropy / max_entropy

        # Pass/Fail 判断
        passed = entropy_ratio >= 0.9

        details = {
            "actual": f"{entropy_ratio:.4f}",
            "threshold": "≥0.9",
            "raw_entropy": f"{shannon_entropy:.4f}",
            "max_entropy": f"{max_entropy:.4f}"
        }

        return passed, details

    def _sequence_pattern_test_with_details(self):
        """序列模式测试，返回Pass/Fail和详细数据"""
        if len(self.seeds) < 2:
            return False, {"error": "需要至少2个种子值"}

        # 转换为整数并计算连续差值
        int_seeds = [int(seed, 16) for seed in self.seeds]
        differences = []

        for i in range(1, len(int_seeds)):
            diff = int_seeds[i] - int_seeds[i-1]
            differences.append(diff)

        # 统计差值频率
        diff_counts = Counter(differences)
        most_common_diff, most_common_count = diff_counts.most_common(1)[0]
        percentage = (most_common_count / len(differences)) * 100

        # Pass/Fail 判断
        passed = percentage < 20

        details = {
            "actual": f"{percentage:.2f}%",
            "threshold": "<20%",
            "most_common_diff": str(most_common_diff),
            "occurrences": str(most_common_count)
        }

        return passed, details

    def _linear_correlation_test_with_details(self):
        """线性相关性测试，返回Pass/Fail和详细数据"""
        if len(self.seeds) < 3:
            return False, {"error": "需要至少3个种子值"}

        # 转换为整数
        int_seeds = [int(seed, 16) for seed in self.seeds]
        indices = list(range(len(int_seeds)))

        # 计算皮尔逊相关系数
        try:
            correlation, _ = stats.pearsonr(indices, int_seeds)

            # Pass/Fail 判断
            passed = abs(correlation) < 0.3

            details = {
                "actual": f"{correlation:.4f}",
                "threshold": "|r| < 0.3",
                "abs_value": f"{abs(correlation):.4f}"
            }

            return passed, details

        except Exception as e:
            return False, {"error": f"计算错误: {e}"}

    def _byte_distribution_test_with_details(self):
        """字节分布测试，返回Pass/Fail和详细数据"""
        if not self.seed_bytes:
            return False, {"error": "没有可分析的种子数据"}

        # 将所有种子的字节合并
        all_bytes = []
        for seed_byte_array in self.seed_bytes:
            all_bytes.extend(seed_byte_array)

        # 基本统计
        mean_value = np.mean(all_bytes)

        # 计算字节熵
        byte_counts = Counter(all_bytes)
        total_bytes = len(all_bytes)
        byte_entropy = 0

        for count in byte_counts.values():
            probability = count / total_bytes
            if probability > 0:
                byte_entropy -= probability * math.log2(probability)

        # 卡方检验
        try:
            # 统计每个字节值(0-255)的出现次数
            observed_frequencies = []
            for i in range(256):
                observed_frequencies.append(byte_counts.get(i, 0))

            # 期望频率(均匀分布)
            expected_frequency = len(all_bytes) / 256
            expected_frequencies = [expected_frequency] * 256

            # 卡方检验
            _, p_value = stats.chisquare(observed_frequencies, expected_frequencies)

            # Pass/Fail 判断
            passed = p_value > 0.05 and byte_entropy > 7.5

            details = {
                "entropy_actual": f"{byte_entropy:.4f}",
                "entropy_threshold": ">7.5",
                "p_value_actual": f"{p_value:.4f}",
                "p_value_threshold": ">0.05",
                "mean_value": f"{mean_value:.2f}"
            }

            return passed, details

        except Exception as e:
            return False, {"error": f"计算错误: {e}"}

    def _time_based_pattern_test_with_details(self):
        """时间模式测试，返回Pass/Fail和详细数据"""
        if len(self.seeds) < 10:
            return False, {"error": "需要至少10个种子值"}

        # 转换为整数并计算连续差值
        int_seeds = [int(seed, 16) for seed in self.seeds]
        differences = []

        for i in range(1, len(int_seeds)):
            diff = int_seeds[i] - int_seeds[i-1]
            differences.append(diff)

        # 检查是否存在固定增量模式
        diff_counts = Counter(differences)

        # 计算差值的标准差
        diff_std = np.std(differences)
        diff_mean = np.mean(differences)

        # 检查最常见差值的占比
        most_common_count = diff_counts.most_common(1)[0][1]
        common_ratio = most_common_count / len(differences)

        # 变异系数
        cv = diff_std / abs(diff_mean) if diff_mean != 0 else float('inf')

        # 检查是否存在类似时间戳的模式
        is_time_based = False
        if cv < 0.1 and common_ratio > 0.3:
            is_time_based = True
        if len(set(differences[:min(100, len(differences))])) < 5:
            is_time_based = True

        # Pass/Fail 判断
        passed = not is_time_based

        details = {
            "cv_actual": f"{cv:.4f}",
            "cv_threshold": ">0.1",
            "ratio_actual": f"{common_ratio:.4f}",
            "ratio_threshold": "<0.3",
            "time_based": "是" if is_time_based else "否"
        }

        return passed, details

    def _repetition_test_with_details(self):
        """重复模式测试，返回Pass/Fail和详细数据"""
        if not self.seeds:
            return False, {"error": "没有可分析的种子数据"}

        # 检查重复值
        seed_counts = Counter(self.seeds)
        duplicates = {seed: count for seed, count in seed_counts.items() if count > 1}

        duplicate_count = sum(count - 1 for count in duplicates.values())
        duplicate_rate = (duplicate_count / len(self.seeds)) * 100

        # Pass/Fail 判断
        passed = duplicate_rate < 1.0

        details = {
            "actual": f"{duplicate_rate:.2f}%",
            "threshold": "<1.0%",
            "duplicate_count": str(duplicate_count),
            "total_seeds": str(len(self.seeds))
        }

        return passed, details

    def _display_detailed_table(self, test_results, test_details):
        """显示详细的测试结果表格"""
        print("\n" + "=" * 100)
        print(Colors.bold("随机种子安全性测试详细结果"))
        print("=" * 100)

        # 定义列宽
        col_widths = [12, 10, 25, 18, 20]
        total_width = sum(col_widths) + len(col_widths) - 1  # 加上分隔符

        # 表格头部
        header_line = "+" + "+".join(["-" * w for w in col_widths]) + "+"
        print(header_line)

        headers = ["测试项目", "状态", "实际值", "参考值/阈值", "描述"]
        header_row = "|" + "|".join([f"{h:^{w}}" for h, w in zip(headers, col_widths)]) + "|"
        print(header_row)
        print(header_line)

        # 测试项目描述
        test_descriptions = {
            '熵分析': '随机性质量检查',
            '序列模式': '连续值模式检测',
            '线性相关性': '线性趋势检测',
            '字节分布': '字节均匀性检查',
            '时间模式': '时间戳模式检测',
            '重复模式': '重复值检查'
        }

        passed_count = 0
        total_count = len(test_results)

        for test_name, passed in test_results.items():
            details = test_details.get(test_name, {})

            # 状态 - 不使用颜色，保持对齐
            if passed:
                status_text = "PASS"
                passed_count += 1
            else:
                status_text = "FAIL"

            # 处理错误情况
            if "error" in details:
                actual_value = "错误"
                threshold = details["error"][:16]
            else:
                # 根据测试类型显示不同的实际值和阈值
                if test_name == '熵分析':
                    actual_value = f"熵比率: {details.get('actual', 'N/A')}"
                    threshold = "≥0.9"
                elif test_name == '序列模式':
                    actual_value = f"占比: {details.get('actual', 'N/A')}"
                    threshold = "<20%"
                elif test_name == '线性相关性':
                    actual_value = f"相关系数: {details.get('actual', 'N/A')}"
                    threshold = "|r| < 0.3"
                elif test_name == '字节分布':
                    entropy = details.get('entropy_actual', 'N/A')
                    p_val = details.get('p_value_actual', 'N/A')
                    actual_value = f"熵:{entropy} p:{p_val}"
                    threshold = ">7.5 & >0.05"
                elif test_name == '时间模式':
                    cv = details.get('cv_actual', 'N/A')
                    ratio = details.get('ratio_actual', 'N/A')
                    if cv == 'N/A':
                        actual_value = "数据不足"
                    else:
                        actual_value = f"CV:{cv[:6]} 比率:{ratio[:6]}"
                    threshold = ">0.1 & <0.3"
                elif test_name == '重复模式':
                    actual_value = f"重复率: {details.get('actual', 'N/A')}"
                    threshold = "<1.0%"
                else:
                    actual_value = "N/A"
                    threshold = "N/A"

            description = test_descriptions.get(test_name, "未知测试")

            # 确保字符串长度适合列宽
            actual_value = actual_value[:23] if len(actual_value) > 23 else actual_value
            threshold = threshold[:16] if len(threshold) > 16 else threshold
            description = description[:18] if len(description) > 18 else description

            # 应用颜色到状态
            if passed:
                status_colored = Colors.green(status_text)
            else:
                status_colored = Colors.red(status_text)

            # 输出行
            row_data = [test_name, status_colored, actual_value, threshold, description]
            row_widths = [col_widths[0], col_widths[1], col_widths[2], col_widths[3], col_widths[4]]

            # 特殊处理状态列的对齐（因为有颜色代码）
            row = f"|{test_name:^{col_widths[0]}}|{status_colored:^{col_widths[1]+10}}|{actual_value:^{col_widths[2]}}|{threshold:^{col_widths[3]}}|{description:^{col_widths[4]}}|"
            print(row)

        print(header_line)

        # 整体评估
        pass_rate = (passed_count / total_count) * 100

        if passed_count == total_count:
            overall_status_text = "PASS"
            overall_status = Colors.green(overall_status_text)
            overall_desc = "所有测试通过"
        elif pass_rate >= 80:
            overall_status_text = "WARN"
            overall_status = Colors.yellow(overall_status_text)
            overall_desc = f"通过率 {pass_rate:.0f}%"
        else:
            overall_status_text = "FAIL"
            overall_status = Colors.red(overall_status_text)
            overall_desc = f"通过率 {pass_rate:.0f}%"

        summary = f"通过: {passed_count}/{total_count}"
        overall_row = f"|{'整体评估':^{col_widths[0]}}|{overall_status:^{col_widths[1]+10}}|{summary:^{col_widths[2]}}|{f'{pass_rate:.1f}%':^{col_widths[3]}}|{overall_desc:^{col_widths[4]}}|"
        print(overall_row)

        print("+" + "+".join(["-" * w for w in col_widths]) + "+")

        # 详细统计信息
        print(f"\n{Colors.bold('详细统计信息:')}")
        print(f"  • 总测试项目: {total_count}")
        print(f"  • 通过项目: {Colors.green(str(passed_count))}")
        print(f"  • 失败项目: {Colors.red(str(total_count - passed_count))}")
        print(f"  • 通过率: {Colors.green(f'{pass_rate:.1f}%') if pass_rate == 100 else (Colors.yellow(f'{pass_rate:.1f}%') if pass_rate >= 80 else Colors.red(f'{pass_rate:.1f}%'))}")

        # 失败项目的详细信息
        if passed_count < total_count:
            print(f"\n{Colors.yellow('失败项目详情:')}")
            for test_name, passed in test_results.items():
                if not passed:
                    details = test_details.get(test_name, {})
                    if "error" in details:
                        print(f"  • {test_name}: {Colors.red(details['error'])}")
                    else:
                        print(f"  • {test_name}: 未达到安全标准")

        if passed_count < total_count:
            print(f"\n{Colors.yellow('安全建议:')}")
            print("  • 检查随机数生成器的实现和配置")
            print("  • 验证种子来源的真随机性")
            print("  • 考虑使用硬件随机数生成器")
            print("  • 定期更新和测试随机数生成算法")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='随机种子安全性分析工具')
    parser.add_argument('-f', '--file', help='包含种子值的文件路径')
    parser.add_argument('-s', '--seeds', nargs='+', help='直接提供的种子值列表')

    args = parser.parse_args()

    analyzer = RandomSeedAnalyzer()

    if args.file:
        if analyzer.load_seeds_from_file(args.file):
            analyzer.run_full_analysis()
    elif args.seeds:
        if analyzer.load_seeds_from_list(args.seeds):
            analyzer.run_full_analysis()
    else:
        print("请提供种子文件(-f)或种子值列表(-s)")
        print("示例:")
        print("  python RandomTest.py -f 2703-seed.log")
        print("  python RandomTest.py -s 4e0470 65be9f c1d55a")


if __name__ == "__main__":
    main()